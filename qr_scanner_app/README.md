# Peekoder

A Flutter mobile application for scanning QR codes using the device's camera with real-time detection and a clean, user-friendly interface.

## Features

✅ **Real-time QR Code Scanning** - Automatically detects QR codes using the device camera
✅ **Clean UI** - Simple and intuitive interface with camera preview and scanning overlay
✅ **Permission Handling** - Proper camera permission management for Android and iOS
✅ **Flashlight Toggle** - Built-in flashlight control for scanning in low light
✅ **URL Detection** - Automatically detects and opens URLs from QR codes
✅ **Copy to Clipboard** - Easy copying of scanned content
✅ **Re-scan Functionality** - Quick re-scan button for continuous scanning
✅ **Cross-platform** - Works on both Android and iOS devices

## Screenshots

The app features:
- **Scanner Screen**: Camera preview with scanning overlay and flashlight toggle
- **Result Screen**: Displays scanned content with action buttons for URLs and text
- **Permission Screen**: Clean permission request interface

## Dependencies

- `mobile_scanner: ^7.0.1` - Modern QR/barcode scanner plugin
- `permission_handler: ^12.0.0+1` - Camera permission handling
- `url_launcher: ^6.3.1` - Opening URLs from QR codes

## Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Android Studio / Xcode for device testing
- Physical device (camera required for QR scanning)

### Installation

1. **Clone or download the project**
2. **Navigate to the project directory**
   ```bash
   cd qr_scanner_app
   ```

3. **Install dependencies**
   ```bash
   flutter pub get
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Platform Setup

#### Android
- Camera permissions are automatically handled
- Minimum SDK version: 21 (Android 5.0)

#### iOS
- Camera usage description is included in Info.plist
- Minimum iOS version: 11.0

## Usage

1. **Launch the app** - Camera opens automatically
2. **Grant camera permission** when prompted
3. **Point camera at QR code** - Detection happens in real-time
4. **View results** - Scanned content appears on result screen
5. **Take action**:
   - Open URLs directly in browser
   - Copy text to clipboard
   - Scan again for more codes

## App Structure

```
lib/
├── main.dart              # App entry point
├── qr_scanner_screen.dart # Main scanner interface
└── result_screen.dart     # Result display and actions
```

## Testing

Run tests with:
```bash
flutter test
```

Run code analysis:
```bash
flutter analyze
```

## Building for Release

### Android APK
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

## Troubleshooting

**Camera not working?**
- Ensure camera permissions are granted
- Test on physical device (camera required)
- Check that camera is not being used by another app

**QR codes not detected?**
- Ensure good lighting conditions
- Hold device steady
- Try using the flashlight toggle
- Make sure QR code is clearly visible and not damaged

## License

This project is created for educational purposes and is free to use and modify.
