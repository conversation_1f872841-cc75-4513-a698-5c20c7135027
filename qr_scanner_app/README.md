# Peekoder

A Flutter mobile application for scanning QR codes using the device's camera with real-time detection and a clean, user-friendly interface.

## Features

✅ **Real-time QR Code Scanning** - Automatically detects QR codes using the device camera
✅ **Gallery Image Scanning** - Scan QR codes from photos in your gallery
✅ **Modern Dark UI** - Beautiful dark theme with cyan accents and smooth animations
✅ **Permission Handling** - Proper camera and photo library permission management
✅ **Flashlight Toggle** - Built-in flashlight control for scanning in low light
✅ **URL Detection** - Automatically detects and opens URLs from QR codes
✅ **Copy to Clipboard** - Easy copying of scanned content
✅ **Re-scan Functionality** - Quick re-scan button for continuous scanning
✅ **Enhanced Overlay** - Custom scanning frame with corner indicators
✅ **Info Dialog** - Built-in help and usage instructions
✅ **Cross-platform** - Works on both Android and iOS devices

## Screenshots

The app features:
- **Scanner Screen**: Full-screen camera preview with modern overlay and controls
- **Gallery Scanning**: Pick images from gallery and scan QR codes from them
- **Result Screen**: Dark-themed result display with enhanced action buttons
- **Permission Screen**: Beautiful permission request with gallery fallback option

## Dependencies

- `mobile_scanner: ^7.0.1` - Modern QR/barcode scanner plugin
- `permission_handler: ^12.0.0+1` - Camera permission handling
- `url_launcher: ^6.3.1` - Opening URLs from QR codes
- `image_picker: ^1.1.2` - Gallery image selection for QR scanning

## Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Android Studio / Xcode for device testing
- Physical device (camera required for QR scanning)

### Installation

1. **Clone or download the project**
2. **Navigate to the project directory**
   ```bash
   cd qr_scanner_app
   ```

3. **Install dependencies**
   ```bash
   flutter pub get
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Platform Setup

#### Android
- Camera permissions are automatically handled
- Gallery access permissions are automatically handled
- Minimum SDK version: 21 (Android 5.0)

#### iOS
- Camera usage description is included in Info.plist
- Photo library usage description is included in Info.plist
- Minimum iOS version: 11.0

## Usage

### Camera Scanning
1. **Launch the app** - Camera opens automatically with modern overlay
2. **Grant camera permission** when prompted (or use gallery option)
3. **Point camera at QR code** - Detection happens in real-time within the frame
4. **View results** - Scanned content appears on enhanced result screen

### Gallery Scanning
1. **Tap Gallery button** on the main screen or permission screen
2. **Select an image** from your photo library
3. **Automatic detection** - App analyzes the image for QR codes
4. **View results** - Same enhanced result screen as camera scanning

### Actions Available
- **Open URLs** directly in browser
- **Copy text** to clipboard
- **Scan again** for more codes
- **Access help** via the info button

## App Structure

```
lib/
├── main.dart              # App entry point
├── qr_scanner_screen.dart # Main scanner interface
└── result_screen.dart     # Result display and actions
```

## Testing

Run tests with:
```bash
flutter test
```

Run code analysis:
```bash
flutter analyze
```

## Building for Release

### Android APK
```bash
flutter build apk --release
```

### iOS
```bash
flutter build ios --release
```

## Troubleshooting

**Camera not working?**
- Ensure camera permissions are granted
- Test on physical device (camera required)
- Check that camera is not being used by another app

**QR codes not detected?**
- Ensure good lighting conditions
- Hold device steady
- Try using the flashlight toggle
- Make sure QR code is clearly visible and not damaged

## License

This project is created for educational purposes and is free to use and modify.
