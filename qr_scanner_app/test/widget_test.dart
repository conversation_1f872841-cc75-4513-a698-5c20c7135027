// This is a basic Flutter widget test for QR Scanner App.

import 'package:flutter_test/flutter_test.dart';

import 'package:qr_scanner_app/main.dart';

void main() {
  testWidgets('Peekoder App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const QRScannerApp());

    // Verify that the app loads with Peekoder title
    expect(find.text('Peekoder'), findsOneWidget);

    // Since camera permission is required, we should see permission request UI
    // when camera permission is not granted
    expect(find.text('Camera Permission Required'), findsOneWidget);
  });
}
