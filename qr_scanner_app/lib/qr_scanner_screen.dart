import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import 'result_screen.dart';

class QRScannerScreen extends StatefulWidget {
  const QRScannerScreen({super.key});

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> with WidgetsBindingObserver {
  MobileScannerController cameraController = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
    facing: CameraFacing.back,
    torchEnabled: false,
  );
  bool _isFlashOn = false;
  bool _hasPermission = false;
  bool _isScanning = true;
  final ImagePicker _imagePicker = ImagePicker();
  String? _lastScannedCode;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _requestCameraPermission();
  }

  Future<void> _requestCameraPermission() async {
    final status = await Permission.camera.request();
    setState(() {
      _hasPermission = status == PermissionStatus.granted;
    });
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
    cameraController.toggleTorch();
  }

  void _onDetect(BarcodeCapture capture) {
    if (!_isScanning || !mounted) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isNotEmpty) {
      // Try to get the best quality barcode data
      String? code;
      for (final barcode in barcodes) {
        if (barcode.rawValue != null && barcode.rawValue!.isNotEmpty) {
          code = barcode.rawValue!;
          break;
        }
        // Fallback to displayValue if rawValue is empty
        if (barcode.displayValue != null && barcode.displayValue!.isNotEmpty) {
          code = barcode.displayValue!;
          break;
        }
      }

      if (code != null && code.isNotEmpty && code != _lastScannedCode) {
        _lastScannedCode = code;
        setState(() {
          _isScanning = false;
        });

        // Add a small delay to ensure UI updates
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ResultScreen(
                  scannedData: code!,
                  onScanAgain: _resetScanning,
                ),
              ),
            );
          }
        });
      }
    }
  }

  void _resetScanning() {
    if (mounted) {
      setState(() {
        _isScanning = true;
        _lastScannedCode = null;
      });
      // Restart the camera controller to ensure fresh scanning
      cameraController.start();
    }
  }

  Future<void> _scanFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100,
        maxWidth: 1920,
        maxHeight: 1920,
      );

      if (image != null && mounted) {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(color: Colors.cyan),
          ),
        );

        try {
          final BarcodeCapture? barcodes = await cameraController.analyzeImage(image.path);

          // Close loading dialog
          if (mounted) Navigator.of(context).pop();

          if (barcodes != null && barcodes.barcodes.isNotEmpty) {
            // Try to get the best quality barcode data
            String? code;
            for (final barcode in barcodes.barcodes) {
              if (barcode.rawValue != null && barcode.rawValue!.isNotEmpty) {
                code = barcode.rawValue!;
                break;
              }
              if (barcode.displayValue != null && barcode.displayValue!.isNotEmpty) {
                code = barcode.displayValue!;
                break;
              }
            }

            if (code != null && code.isNotEmpty && mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ResultScreen(
                    scannedData: code!,
                    onScanAgain: _resetScanning,
                  ),
                ),
              );
            } else {
              if (mounted) _showNoQRCodeDialog();
            }
          } else {
            if (mounted) _showNoQRCodeDialog();
          }
        } catch (e) {
          // Close loading dialog if still open
          if (mounted) Navigator.of(context).pop();
          if (mounted) _showErrorDialog('Error analyzing image: ${e.toString()}');
        }
      }
    } catch (e) {
      if (mounted) _showErrorDialog('Error selecting image: ${e.toString()}');
    }
  }

  void _showNoQRCodeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.info_outline, color: Colors.orange),
              SizedBox(width: 8),
              Text('No QR Code Found'),
            ],
          ),
          content: const Text('No QR code was detected in the selected image. Please try another image.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red),
              SizedBox(width: 8),
              Text('Error'),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.qr_code, color: Colors.cyan),
              SizedBox(width: 8),
              Text('How to Use Peekoder'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('📷 Camera Scanning:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Point your camera at a QR code\n• Keep the code within the frame\n• Wait for automatic detection\n'),
              Text('🖼️ Gallery Scanning:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Tap the Gallery button\n• Select an image with a QR code\n• The app will analyze the image\n'),
              Text('💡 Tips:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('• Use the flashlight in low light\n• Ensure QR codes are clear and unobstructed\n• Try different angles if scanning fails'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Got it!'),
            ),
          ],
        );
      },
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (!cameraController.value.isInitialized) return;

    if (state == AppLifecycleState.inactive) {
      cameraController.stop();
    } else if (state == AppLifecycleState.resumed) {
      cameraController.start();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    cameraController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasPermission) {
      return Scaffold(
        backgroundColor: const Color(0xFF1A1A1A),
        appBar: AppBar(
          title: const Text(
            'Peekoder',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(30),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(30),
                  decoration: BoxDecoration(
                    color: Colors.cyan.withOpacity(0.1),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.cyan.withOpacity(0.3), width: 2),
                  ),
                  child: const Icon(
                    Icons.camera_alt_outlined,
                    size: 80,
                    color: Colors.cyan,
                  ),
                ),
                const SizedBox(height: 30),
                const Text(
                  'Camera Permission Required',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 15),
                Text(
                  'Peekoder needs camera access to scan QR codes in real-time. You can also scan QR codes from your photo gallery.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[300],
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 40),
                ElevatedButton(
                  onPressed: _requestCameraPermission,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.cyan,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 0,
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.camera_alt),
                      SizedBox(width: 8),
                      Text(
                        'Grant Camera Permission',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: _scanFromGallery,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.cyan,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.photo_library),
                      SizedBox(width: 8),
                      Text('Scan from Gallery Instead'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Peekoder',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _toggleFlash,
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _isFlashOn ? Colors.yellow.withOpacity(0.3) : Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: _isFlashOn ? Colors.yellow : Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Stack(
        children: [
          // Camera view
          Positioned.fill(
            child: MobileScanner(
              controller: cameraController,
              onDetect: _onDetect,
            ),
          ),

          // Overlay with scanning frame
          Positioned.fill(
            child: Container(
              decoration: ShapeDecoration(
                shape: QrScannerOverlayShape(
                  borderColor: Colors.cyan,
                  borderRadius: 20,
                  borderLength: 40,
                  borderWidth: 4,
                  cutOutSize: 280,
                  overlayColor: Colors.black.withOpacity(0.7),
                ),
              ),
            ),
          ),

          // Top instruction text
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Text(
                _isScanning
                    ? 'Position QR code within the frame'
                    : 'Processing...',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),

          // Bottom controls
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(30),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.8),
                    Colors.black,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Gallery button
                  _buildControlButton(
                    icon: Icons.photo_library,
                    label: 'Gallery',
                    onTap: _scanFromGallery,
                  ),

                  // Center scanning indicator
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: _isScanning ? Colors.cyan.withOpacity(0.2) : Colors.orange.withOpacity(0.2),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: _isScanning ? Colors.cyan : Colors.orange,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _isScanning ? Icons.qr_code_scanner : Icons.hourglass_empty,
                      color: _isScanning ? Colors.cyan : Colors.orange,
                      size: 30,
                    ),
                  ),

                  // Settings/Info button
                  _buildControlButton(
                    icon: Icons.info_outline,
                    label: 'Info',
                    onTap: _showInfoDialog,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class QrScannerOverlayShape extends ShapeBorder {
  const QrScannerOverlayShape({
    this.borderColor = Colors.cyan,
    this.borderWidth = 4.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 180),
    this.borderRadius = 20,
    this.borderLength = 40,
    double? cutOutSize,
  }) : cutOutSize = cutOutSize ?? 280;

  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutSize;

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..quadraticBezierTo(rect.left, rect.top, rect.left + borderRadius, rect.top)
        ..lineTo(rect.right, rect.top);
    }

    return getLeftTopPath(rect)
      ..lineTo(rect.right, rect.bottom)
      ..lineTo(rect.left, rect.bottom)
      ..lineTo(rect.left, rect.top);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final height = rect.height;
    final borderOffset = borderWidth / 2;
    final mCutOutSize = cutOutSize < width ? cutOutSize : width - borderOffset;

    final backgroundPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final boxPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOut;

    final cutOutRect = Rect.fromLTWH(
      rect.left + (width - mCutOutSize) / 2 + borderOffset,
      rect.top + (height - mCutOutSize) / 2 + borderOffset,
      mCutOutSize - borderOffset * 2,
      mCutOutSize - borderOffset * 2,
    );

    canvas
      ..saveLayer(
        rect,
        backgroundPaint,
      )
      ..drawRect(rect, backgroundPaint)
      ..drawRRect(
        RRect.fromRectAndRadius(
          cutOutRect,
          Radius.circular(borderRadius),
        ),
        boxPaint,
      )
      ..restore();

    final borderRect = RRect.fromRectAndRadius(
      cutOutRect,
      Radius.circular(borderRadius),
    );

    // Draw corner borders
    final path = Path()
      // Top left
      ..moveTo(borderRect.left, borderRect.top + borderLength)
      ..lineTo(borderRect.left, borderRect.top + borderRadius)
      ..quadraticBezierTo(borderRect.left, borderRect.top, borderRect.left + borderRadius, borderRect.top)
      ..lineTo(borderRect.left + borderLength, borderRect.top)
      // Top right
      ..moveTo(borderRect.right - borderLength, borderRect.top)
      ..lineTo(borderRect.right - borderRadius, borderRect.top)
      ..quadraticBezierTo(borderRect.right, borderRect.top, borderRect.right, borderRect.top + borderRadius)
      ..lineTo(borderRect.right, borderRect.top + borderLength)
      // Bottom right
      ..moveTo(borderRect.right, borderRect.bottom - borderLength)
      ..lineTo(borderRect.right, borderRect.bottom - borderRadius)
      ..quadraticBezierTo(borderRect.right, borderRect.bottom, borderRect.right - borderRadius, borderRect.bottom)
      ..lineTo(borderRect.right - borderLength, borderRect.bottom)
      // Bottom left
      ..moveTo(borderRect.left + borderLength, borderRect.bottom)
      ..lineTo(borderRect.left + borderRadius, borderRect.bottom)
      ..quadraticBezierTo(borderRect.left, borderRect.bottom, borderRect.left, borderRect.bottom - borderRadius)
      ..lineTo(borderRect.left, borderRect.bottom - borderLength);

    canvas.drawPath(path, borderPaint);
  }

  @override
  ShapeBorder scale(double t) {
    return QrScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
    );
  }
}
